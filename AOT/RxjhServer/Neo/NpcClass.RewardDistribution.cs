using System;
using System.Linq;
using HeroYulgang.Helpers;

namespace RxjhServer
{
    public partial class NpcClass
    {
        /// <summary>
        /// Phân phối rewards cho players một cách tối ưu
        /// </summary>
        /// <param name="rewards"><PERSON><PERSON><PERSON> cần phân phối</param>
        /// <param name="killer">Player đã giết NPC</param>
        public void DistributeRewardsOptimized(NpcRewards rewards, Players killer)
        {
            try
            {
                if (rewards.experience <= 0.0 || !killer.LookInNpc(700, this))
                {
                    return;
                }

                if (killer.TeamID != 0)
                {
                    DistributeToTeam(rewards, killer);
                }
                else
                {
                    DistributeToIndividual(rewards, killer);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"NpcClass.DistributeRewardsOptimized error: {ex.Message}");
            }
        }

        /// <summary>
        /// Phân phối rewards cho team
        /// </summary>
        private void DistributeToTeam(NpcRewards rewards, Players killer)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Debug,"DistributeToTeam called");
                var teamDamage = CalculateTeamTotalDamage(killer.TeamID);
                if (teamDamage <= 0) return;

                if (teamDamage > Max_Rxjh_HP)
                {
                    teamDamage = Max_Rxjh_HP;
                }

                // Tính toán rewards theo tỷ lệ damage
                var adjustedRewards = CalculateAdjustedRewards(rewards, teamDamage);

                if (!World.WToDoi.TryGetValue(killer.TeamID, out var team))
                {
                    // Fallback to individual distribution
                    DistributeToIndividual(rewards, killer);
                    return;
                }

                // Kiểm tra level gap
                if (!CheckLevelGap(killer)) return;

                // Tính toán bonus team
                var teamBonus = CalculateTeamBonus(team.ToDoi_NguoiChoi.Count);
                
                // Phân phối cho từng thành viên team
                foreach (var member in team.ToDoi_NguoiChoi.Values)
                {
                    if (!CheckMemberEligibility(member)) continue;
                    
                    var memberRewards = CalculateMemberRewards(adjustedRewards, teamBonus, team.ToDoi_NguoiChoi.Count);
                    ApplyRewardsToPlayer(member, memberRewards);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"NpcClass.DistributeToTeam error: {ex.Message}");
                // Fallback to individual distribution
                DistributeToIndividual(rewards, killer);
            }
        }

        /// <summary>
        /// Phân phối rewards cho cá nhân
        /// </summary>
        private void DistributeToIndividual(NpcRewards rewards, Players player)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Debug,"DistributeToIndividual called");
                var personalDamage = CalculatePersonalDamage(player);
                if (personalDamage <= 0) return;

                if (personalDamage > Max_Rxjh_HP)
                {
                    personalDamage = Max_Rxjh_HP;
                }

                // Tính toán rewards theo tỷ lệ damage
                var adjustedRewards = CalculateAdjustedRewards(rewards, personalDamage);
                
                // Kiểm tra level gap
                if (!CheckLevelGap(player)) return;

                // Áp dụng các bonus cá nhân
                var finalRewards = ApplyPersonalBonuses(adjustedRewards, player);
                
                // Áp dụng rewards
                ApplyRewardsToPlayer(player, finalRewards);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"NpcClass.DistributeToIndividual error: {ex.Message}");
            }
        }

        /// <summary>
        /// Tính toán tổng damage của team
        /// </summary>
        private int CalculateTeamTotalDamage(int teamId)
        {
            try
            {
                return PlayerTargetList
                    .Where(p => p.TeamID == teamId)
                    .Sum(p => p.DamageDealt);
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Tính toán damage cá nhân
        /// </summary>
        private int CalculatePersonalDamage(Players player)
        {
            try
            {
                var playerData = PlayerTargetList.FirstOrDefault(p => p.PlayID == player.SessionID);
                return playerData?.DamageDealt ?? 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Tính toán rewards đã điều chỉnh theo damage
        /// </summary>
        private NpcRewards CalculateAdjustedRewards(NpcRewards originalRewards, int damage)
        {
            var ratio = (double)damage / Max_Rxjh_HP;
            return new NpcRewards
            {
                experience = originalRewards.experience * ratio,
                lichLuyen = originalRewards.lichLuyen * ratio,
                money = originalRewards.money * ratio,
                thangThienLichLuyen = originalRewards.thangThienLichLuyen * ratio
            };
        }

        /// <summary>
        /// Kiểm tra level gap
        /// </summary>
        private bool CheckLevelGap(Players player)
        {
            var levelDiff = Math.Abs(player.Player_Level - Level);
            return levelDiff <= World.LayDuoc_KinhNghiem_CapDo_ChenhLech;
        }

        /// <summary>
        /// Kiểm tra tính hợp lệ của thành viên team
        /// </summary>
        private bool CheckMemberEligibility(Players member)
        {
            return FindPlayers(700, member) &&
                   member.NhanVat_HP > 0 &&
                   !member.PlayerTuVong &&
                   CheckLevelGap(member);
        }

        /// <summary>
        /// Tính toán bonus team
        /// </summary>
        private (double expBonus, double skillBonus, double moneyBonus, double ttBonus) CalculateTeamBonus(int memberCount)
        {
            return (
                expBonus: 1.0 + memberCount * World.Rate_Exp_Party,
                skillBonus: 1.0 + memberCount * World.Rate_KyNang_Party,
                moneyBonus: 1.0 + memberCount * World.Rate_Gold_Party,
                ttBonus: 1.0 + memberCount * World.Rate_KyNang_TT_Party
            );
        }

        /// <summary>
        /// Tính toán rewards cho thành viên team
        /// </summary>
        private NpcRewards CalculateMemberRewards(NpcRewards baseRewards, 
            (double expBonus, double skillBonus, double moneyBonus, double ttBonus) teamBonus, 
            int memberCount)
        {
            return new NpcRewards
            {
                experience = baseRewards.experience * teamBonus.expBonus / memberCount,
                lichLuyen = baseRewards.lichLuyen * teamBonus.skillBonus / memberCount,
                money = baseRewards.money * teamBonus.moneyBonus / memberCount,
                thangThienLichLuyen = baseRewards.thangThienLichLuyen * teamBonus.ttBonus / memberCount
            };
        }

        /// <summary>
        /// Áp dụng bonus cá nhân
        /// </summary>
        private NpcRewards ApplyPersonalBonuses(NpcRewards baseRewards, Players player)
        {
            // Áp dụng các bonus từ equipment, buffs, etc.
            var expMultiplier = 1.0 + player.NhanVat_ThemVao_PhanTramKinhNghiem + 
                               player.FLD_NhanVat_ThemVao_PhanTramKinhNghiem;
            
            var moneyMultiplier = 1.0 + player.FLD_TrangBi_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram +
                                 player.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram;

            return new NpcRewards
            {
                experience = baseRewards.experience * expMultiplier,
                lichLuyen = baseRewards.lichLuyen,
                money = baseRewards.money * moneyMultiplier,
                thangThienLichLuyen = baseRewards.thangThienLichLuyen
            };
        }

        /// <summary>
        /// Áp dụng rewards cho player
        /// </summary>
        private void ApplyRewardsToPlayer(Players player, NpcRewards rewards)
        {
            try
            {
                // Áp dụng level-based multipliers
                LogHelper.WriteLine(LogLevel.Debug,"ApplyRewardsToPlayer called");
                var finalExp = ApplyLevelMultiplier(rewards.experience, player);
                
                // Cập nhật experience
                if (player.Player_Level < World.GioiHan_Level_CaoNhat)
                {
                    player.CharacterExperience += (long)finalExp;
                    player.SpiritOrbBag(3, (long)finalExp);
                }

                // Cập nhật skill experience
                player.Player_ExpErience += (int)rewards.lichLuyen;
                player.ThangThienLichLuyen_TangKinhNghiemNhanDuoc_HienTai += (int)rewards.thangThienLichLuyen;

                // Cập nhật money
                player.Player_Money += (uint)rewards.money;
                player.TipsForGettingMoney((uint)rewards.money);

                // Cập nhật UI
                player.TinhToan_NhanVatCoBan_DuLieu3();
                player.UpdateKinhNghiemVaTraiNghiem();
                player.UpdateMoneyAndWeight();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"NpcClass.ApplyRewardsToPlayer error: {ex.Message}");
            }
        }

        /// <summary>
        /// Áp dụng level multiplier
        /// </summary>
        private double ApplyLevelMultiplier(double baseExp, Players player)
        {
            // Áp dụng các multiplier dựa trên level
            if (player.Player_Level < 10) return baseExp * World.TC_0_EXP;
            if (player.Player_Level < 35) return baseExp * World.TC_1_EXP;
            if (player.Player_Level < 60) return baseExp * World.TC_2_EXP;
            if (player.Player_Level < 80) return baseExp * World.TC_3_EXP;
            if (player.Player_Level < 100) return baseExp * World.TC_4_EXP;
            if (player.Player_Level < 115) return baseExp * World.TC_5_EXP;
            if (player.Player_Level < 120) return baseExp * World.TT_1_EXP;
            if (player.Player_Level < 130) return baseExp * World.TT_2_EXP;
            if (player.Player_Level < 140) return baseExp * World.TT_3_EXP;
            if (player.Player_Level < 150) return baseExp * World.TT_4_EXP;
            if (player.Player_Level < 160) return baseExp * World.TT_5_EXP;
            return baseExp * World.TT_6_EXP;
        }
    }
}
