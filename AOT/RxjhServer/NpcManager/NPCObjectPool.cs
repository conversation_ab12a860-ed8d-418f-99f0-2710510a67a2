using HeroYulgang.Helpers;
using System;
using System.Collections.Concurrent;

namespace RxjhServer.NpcManager
{
    /// <summary>
    /// Object pool for frequently allocated NPC-related objects to reduce GC pressure
    /// </summary>
    public static class NPCObjectPool
    {
        // Object pools
        private static readonly ConcurrentQueue<PlayGjClass> _playGjPool = new();
        private static readonly ConcurrentQueue<byte[]> _packetPool = new();
        
        // Pool size limits
        private const int MAX_PLAYGJ_POOL_SIZE = 1000;
        private const int MAX_PACKET_POOL_SIZE = 100;
        private const int MAX_TEAM_POOL_SIZE = 200;
        private const int MAX_PACKET_SIZE = 1024;
        
        // Statistics
        private static long _playGjRented = 0;
        private static long _playGjReturned = 0;
        private static long _packetsRented = 0;
        private static long _packetsReturned = 0;
        
        #region PlayGjClass Pool
        
        /// <summary>
        /// Rent a PlayGjClass object from the pool
        /// </summary>
        /// <returns>Clean PlayGjClass object</returns>
        public static PlayGjClass RentPlayGjClass()
        {
            _playGjRented++;
            
            if (_playGjPool.TryDequeue(out var obj))
            {
                // Reset object state
                obj.PlayID = 0;
                obj.DamageDealt = 0;
                obj.Gjsl = 0;
                return obj;
            }
            
            // Create new if pool is empty
            return new PlayGjClass();
        }
        
        /// <summary>
        /// Return a PlayGjClass object to the pool
        /// </summary>
        /// <param name="obj">Object to return</param>
        public static void ReturnPlayGjClass(PlayGjClass obj)
        {
            if (obj == null) return;
            
            _playGjReturned++;
            
            // Only return to pool if not at capacity
            if (_playGjPool.Count < MAX_PLAYGJ_POOL_SIZE)
            {
                _playGjPool.Enqueue(obj);
            }
        }
        
        #endregion
        
        #region Packet Buffer Pool
        
        /// <summary>
        /// Rent a byte array for packet data
        /// </summary>
        /// <param name="size">Required buffer size</param>
        /// <returns>Byte array of at least the requested size</returns>
        public static byte[] RentPacketBuffer(int size)
        {
            _packetsRented++;
            
            if (size <= MAX_PACKET_SIZE && _packetPool.TryDequeue(out var buffer))
            {
                // Clear the buffer before returning
                Array.Clear(buffer, 0, Math.Min(size, buffer.Length));
                return buffer;
            }
            
            // Create new buffer if pool is empty or size is too large
            return new byte[size];
        }
        
        /// <summary>
        /// Return a byte array to the pool
        /// </summary>
        /// <param name="buffer">Buffer to return</param>
        public static void ReturnPacketBuffer(byte[] buffer)
        {
            if (buffer == null) return;
            
            _packetsReturned++;
            
            // Only return to pool if size is appropriate and not at capacity
            if (buffer.Length <= MAX_PACKET_SIZE && _packetPool.Count < MAX_PACKET_POOL_SIZE)
            {
                _packetPool.Enqueue(buffer);
            }
        }
        
        #endregion
        
        #region Statistics and Management
        
        /// <summary>
        /// Get pool statistics
        /// </summary>
        /// <returns>Statistics about pool usage</returns>
        public static PoolStatistics GetStatistics()
        {
            return new PoolStatistics
            {
                PlayGjPoolSize = _playGjPool.Count,
                PacketPoolSize = _packetPool.Count,
                PlayGjRented = _playGjRented,
                PlayGjReturned = _playGjReturned,
                PacketsRented = _packetsRented,
                PacketsReturned = _packetsReturned,
                PlayGjHitRate = _playGjRented > 0 ? (double)_playGjReturned / _playGjRented : 0,
                PacketHitRate = _packetsRented > 0 ? (double)_packetsReturned / _packetsRented : 0
            };
        }
        
        /// <summary>
        /// Clear all pools (for cleanup or testing)
        /// </summary>
        public static void ClearAllPools()
        {
            try
            {
                while (_playGjPool.TryDequeue(out _)) { }
                while (_packetPool.TryDequeue(out _)) { }
                
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error clearing object pools: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Trim pools to reduce memory usage
        /// </summary>
        /// <param name="targetSize">Target size for each pool</param>
        public static void TrimPools(int targetSize = 50)
        {
            try
            {
                TrimPool(_playGjPool, targetSize);
                TrimPool(_packetPool, targetSize);
                
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error trimming object pools: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Trim a specific pool to target size
        /// </summary>
        private static void TrimPool<T>(ConcurrentQueue<T> pool, int targetSize)
        {
            while (pool.Count > targetSize && pool.TryDequeue(out _))
            {
                // Remove excess items
            }
        }
        
        /// <summary>
        /// Log pool statistics
        /// </summary>
        public static void LogStatistics()
        {
            try
            {
                var stats = GetStatistics();
                LogHelper.WriteLine(LogLevel.Info, 
                    $"NPCObjectPool Stats - PlayGj: {stats.PlayGjPoolSize} pooled, {stats.PlayGjHitRate:P1} hit rate | " +
                    $"Packets: {stats.PacketPoolSize} pooled, {stats.PacketHitRate:P1} hit rate | " +
                    $"Teams: {stats.TeamPoolSize} pooled");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error logging pool statistics: {ex.Message}");
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// Statistics about object pool usage
    /// </summary>
    public class PoolStatistics
    {
        public int PlayGjPoolSize { get; set; }
        public int PacketPoolSize { get; set; }
        public int TeamPoolSize { get; set; }
        public long PlayGjRented { get; set; }
        public long PlayGjReturned { get; set; }
        public long PacketsRented { get; set; }
        public long PacketsReturned { get; set; }
        public double PlayGjHitRate { get; set; }
        public double PacketHitRate { get; set; }
    }
}
